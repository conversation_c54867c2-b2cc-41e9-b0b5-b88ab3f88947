'use client';

import { useState } from 'react';
import AboutSection from '../components/AboutSection';
import SelectChallenge from '../components/SelectChallenge';
import ShowChallenge from '../components/ShowChallenge';
import SocialAndFeedback from '../components/SocialAndFeedback';
import { getRandomTeam, getChallenges, getRandomChallenge, supabase } from '../lib/supabase';
import { useLanguage } from '../lib/LanguageContext';

export default function Home() {
  const { t, language } = useLanguage();
  const [team, setTeam] = useState(null);
  const [challenges, setChallenges] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [selectedDifficulties, setSelectedDifficulties] = useState({
    team: '',
    challenge: ''
  });

  // Funzione per tracciare eventi GA
  const trackEvent = (eventName, eventParams = {}) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', eventName, eventParams);
    }
  };

  const generateChallenge = async (teamDifficulty, challengeDifficulty, lang = 'it', continents = []) => {
    setLoading(true);
    setError(null);

    // Traccia l'evento di generazione sfida
    trackEvent('generate_challenge', {
      team_difficulty: teamDifficulty,
      challenge_difficulty: challengeDifficulty,
      language: lang
    });

    // Salva le difficoltà selezionate per poterle riutilizzare durante la rigenerazione
    setSelectedDifficulties({
      team: teamDifficulty,
      challenge: challengeDifficulty,
      continents: continents
    });

    try {
      console.log(`Generating challenge with team difficulty: ${teamDifficulty}, challenge difficulty: ${challengeDifficulty}, language: ${lang}, continents: ${continents.join(', ')}`);

      // Get random team based on difficulty and continents
      const randomTeam = await getRandomTeam(teamDifficulty, continents);

      // Get random challenges based on difficulty
      const randomChallenges = await getChallenges(challengeDifficulty, lang);

      if (!randomTeam) {
        throw new Error(t.errorTeam);
      }

      if (!randomChallenges || Object.keys(randomChallenges).length === 0) {
        throw new Error(t.errorChallenge);
      }

      setTeam(randomTeam);
      setChallenges(randomChallenges);
    } catch (err) {
      console.error('Error generating challenge:', err);
      setError(err.message || t.errorGeneric);
    } finally {
      setLoading(false);
    }
  };

  // Stato per tenere traccia degli elementi bloccati
  const [lockedItems, setLockedItems] = useState({
    team: false,
    obiettivi: false,
    rosa: false,
    tattica: false,
  });

  const regenerateChallenge = async (currentLockedItems) => {
    setLoading(true);
    setError(null);

    // Traccia l'evento di rigenerazione sfida
    trackEvent('regenerate_challenge', {
      team_difficulty: selectedDifficulties.team,
      challenge_difficulty: selectedDifficulties.challenge,
      language: language
    });

    // Aggiorniamo lo stato dei lock con quello corrente
    setLockedItems(currentLockedItems);

    try {
      let newTeam = team;
      let newChallenges = { ...challenges };

      // If team is not locked, get a new random team
      if (!currentLockedItems.team && team) {
        // Usa la difficoltà e i continenti salvati in precedenza
        const continents = selectedDifficulties.continents || [];
        const randomTeam = await getRandomTeam(selectedDifficulties.team, continents);
        if (randomTeam) {
          newTeam = randomTeam;
        }
      }

      // For each challenge category that is not locked, get a new random challenge
      for (const category of ['obiettivi', 'rosa', 'tattica']) {
        if (!currentLockedItems[category] && challenges && challenges[category]) {
          try {
            // Utilizziamo la funzione esistente per ottenere una nuova sfida casuale
            const newChallenge = await getRandomChallenge(
              selectedDifficulties.challenge,
              category,
              language
            );

            if (newChallenge && newChallenge.id !== challenges[category].id) {
              newChallenges[category] = newChallenge;
            }
          } catch (err) {
            console.error(`Error regenerating challenge for category ${category}:`, err);
          }
        }
      }

      setTeam(newTeam);
      setChallenges(newChallenges);
    } catch (err) {
      console.error('Error regenerating challenge:', err);
      setError(err.message || t.errorGeneric);
    } finally {
      setLoading(false);
    }
  };

  const shareChallenge = async () => {
    // Traccia l'evento di condivisione sfida
    trackEvent('share_challenge', {
      team_name: team?.name || 'unknown',
      language: language
    });

    // Resto del codice esistente...
  };

  return (
    <div className="min-h-screen bg-bgMain text-textPrimary">
      {/* About Section */}
      <div className="bg-gradient-to-br from-bgMain via-bgBox to-bgMain">
        <div className="max-w-6xl mx-auto pt-4 pb-8 px-4 md:pt-6 md:pb-12 md:px-8">
          <AboutSection />
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-6xl mx-auto px-4 md:px-8">
        {/* Challenge Generator and Results Section - Side by side on desktop */}
        <div className="py-8 grid lg:grid-cols-2 gap-8">
          {/* Left Column - Challenge Generator */}
          <div className="space-y-6">
            <SelectChallenge onGenerateChallenge={generateChallenge} />
          </div>

          {/* Right Column - Results */}
          <div className="space-y-6">
            {/* Loading State */}
            {loading && (
              <div className="text-center py-12">
                <div className="inline-flex items-center px-4 py-2 font-semibold leading-6 text-sm shadow rounded-md text-textPrimary bg-bgBox">
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-accent" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  {t.loading}
                </div>
              </div>
            )}

            {/* Error State */}
            {error && (
              <div className="mb-8">
                <div className="bg-red-900/20 border border-red-500/50 text-red-200 p-4 rounded-lg">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <p className="text-sm">{error}</p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Challenge Results */}
            {team && challenges && !loading && (
              <ShowChallenge
                team={team}
                challenges={challenges}
                onRegenerate={regenerateChallenge}
                initialLockedState={lockedItems}
              />
            )}
          </div>
        </div>

        {/* Social and Feedback Section */}
        <div className="py-12">
          <SocialAndFeedback />
        </div>

        {/* Footer */}
        <footer className="py-8 border-t border-textSecondary/10">
          <div className="text-center">
            <p className="text-textSecondary text-sm">
              FM Challenger &copy; {new Date().getFullYear()} - {t.footer}
            </p>
            <div className="mt-2 flex justify-center space-x-4 text-xs text-textSecondary">
              <a href="/privacy" className="hover:text-textPrimary transition-colors">
                {language === 'it' ? 'Privacy Policy' : 'Privacy Policy'}
              </a>
              <span>•</span>
              <a href="/terms" className="hover:text-textPrimary transition-colors">
                {language === 'it' ? 'Termini di Servizio' : 'Terms of Service'}
              </a>
              <span>•</span>
              <a href="/contact" className="hover:text-textPrimary transition-colors">
                {language === 'it' ? 'Contatti' : 'Contact'}
              </a>
            </div>
          </div>
        </footer>
      </div>
    </div>
  );
}

