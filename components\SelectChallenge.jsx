'use client';

import { useState, useEffect } from 'react';
import { useLanguage } from '../lib/LanguageContext';
import { getTeamDifficultyLevels, getChallengeDifficultyLevels } from '../lib/supabase';

export default function SelectChallenge({ onGenerateChallenge }) {
  const [teamDifficulty, setTeamDifficulty] = useState('');
  const [challengeDifficulty, setChallengeDifficulty] = useState('');
  const [teamDifficultyLevels, setTeamDifficultyLevels] = useState([]);
  const [challengeDifficultyLevels, setChallengeDifficultyLevels] = useState([]);
  const [selectedTeamDescription, setSelectedTeamDescription] = useState('');
  const [selectedChallengeDescription, setSelectedChallengeDescription] = useState('');
  const [loading, setLoading] = useState(true);
  const [selectedContinents, setSelectedContinents] = useState([]);
  const { t, language } = useLanguage();

  // Lista dei continenti disponibili
  const continents = [
    { value: 'Europe', label: 'Europa' },
    { value: 'Asia', label: 'Asia' },
    { value: 'North America', label: 'Nord America' },
    { value: 'South America', label: 'Sud America' },
    { value: 'Africa', label: 'Africa' },
  ];

  // Carica i livelli di difficoltà dal database quando cambia la lingua
  useEffect(() => {
    const fetchDifficultyLevels = async () => {
      setLoading(true);
      try {
        // Recupera i livelli di difficoltà per le squadre
        const teamLevels = await getTeamDifficultyLevels(language);
        setTeamDifficultyLevels(teamLevels);

        // Recupera i livelli di difficoltà per le sfide
        const challengeLevels = await getChallengeDifficultyLevels(language);
        setChallengeDifficultyLevels(challengeLevels);

        // Resetta le selezioni quando cambia la lingua
        setTeamDifficulty('');
        setChallengeDifficulty('');
        setSelectedTeamDescription('');
        setSelectedChallengeDescription('');
      } catch (error) {
        console.error('Error fetching difficulty levels:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchDifficultyLevels();
  }, [language]);

  // Aggiorna la descrizione quando cambia la difficoltà della squadra
  const handleTeamDifficultyChange = (e) => {
    const value = e.target.value;
    setTeamDifficulty(value);

    // Trova la descrizione corrispondente
    const selectedLevel = teamDifficultyLevels.find(level => level.value === value);
    setSelectedTeamDescription(selectedLevel ? selectedLevel.description : '');
  };

  // Aggiorna la descrizione quando cambia la difficoltà della sfida
  const handleChallengeDifficultyChange = (e) => {
    const value = e.target.value;
    setChallengeDifficulty(value);

    // Trova la descrizione corrispondente
    const selectedLevel = challengeDifficultyLevels.find(level => level.value === value);
    setSelectedChallengeDescription(selectedLevel ? selectedLevel.description : '');
  };

  // Gestisce il cambio di stato dei checkbox dei continenti
  const handleContinentChange = (continent) => {
    setSelectedContinents(prev => {
      if (prev.includes(continent)) {
        return prev.filter(c => c !== continent);
      } else {
        return [...prev, continent];
      }
    });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (teamDifficulty && challengeDifficulty) {
      onGenerateChallenge(teamDifficulty, challengeDifficulty, language, selectedContinents);
    }
  };

  return (
    <div className="card w-full h-full">
      <h2 className="text-xl font-semibold mb-4 text-textTitle">
        {t.teamDifficulty}
      </h2>
      {loading ? (
        <div className="text-center py-4">
          <p>{t.loading}</p>
        </div>
      ) : (
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label htmlFor="teamDifficulty" className="block mb-2 text-textPrimary">
              {t.teamDifficulty}:
            </label>
            <select
              id="teamDifficulty"
              value={teamDifficulty}
              onChange={handleTeamDifficultyChange}
              className="select-custom w-full"
              required
              aria-label={t.teamDifficulty}
            >
              <option value="">{t.select}</option>
              {teamDifficultyLevels.map((level) => (
                <option key={level.value} value={level.value}>
                  {level.name}
                </option>
              ))}
            </select>

            {/* Mostra la descrizione della difficoltà selezionata */}
            {selectedTeamDescription && (
              <div className="mt-2 p-3 bg-bgBox/50 rounded-md text-sm text-textPrimary">
                {selectedTeamDescription}
              </div>
            )}
          </div>

          <div>
            <label htmlFor="challengeDifficulty" className="block mb-2 text-textPrimary">
              {t.challengeDifficulty}:
            </label>
            <select
              id="challengeDifficulty"
              value={challengeDifficulty}
              onChange={handleChallengeDifficultyChange}
              className="select-custom w-full"
              required
              aria-label={t.challengeDifficulty}
            >
              <option value="">{t.select}</option>
              {challengeDifficultyLevels.map((level) => (
                <option key={level.value} value={level.value}>
                  {level.name}
                </option>
              ))}
            </select>

            {/* Mostra la descrizione della difficoltà selezionata */}
            {selectedChallengeDescription && (
              <div className="mt-2 p-3 bg-bgBox/50 rounded-md text-sm text-textPrimary">
                {selectedChallengeDescription}
              </div>
            )}
          </div>

          <div className="mt-6">
            <h3 className="block mb-2 text-textPrimary font-medium">{t.filterByContinent}</h3>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
              {continents.map((continent) => (
                <div key={continent.value} className="flex items-center">
                  <input
                    type="checkbox"
                    id={`continent-${continent.value}`}
                    checked={selectedContinents.includes(continent.value)}
                    onChange={() => handleContinentChange(continent.value)}
                    className="mr-2 h-4 w-4 accent-accent"
                  />
                  <label htmlFor={`continent-${continent.value}`} className="text-textPrimary text-sm">
                    {continent.label}
                  </label>
                </div>
              ))}
            </div>
            {selectedContinents.length > 0 && (
              <p className="mt-2 text-xs text-textSecondary">
                {t.activeFilter}
              </p>
            )}
          </div>

          <button
            type="submit"
            className="btn-primary w-full mt-6"
            disabled={!teamDifficulty || !challengeDifficulty}
          >
            {t.generateChallenge}
          </button>
        </form>
      )}
    </div>
  );
}
