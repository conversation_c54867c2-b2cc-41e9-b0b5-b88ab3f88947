/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./components/**/*.{js,ts,jsx,tsx,mdx}",
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      colors: {
        background: "var(--background)",
        foreground: "var(--foreground)",
        // Nuova palette colori richiesta
        primary: "#0E1621",             // Blu scurissimo per topbar
        secondary: "#0E1621",           // Blu scurissimo (quasi nero) per sfondi scuri e footer/header
        background: "#15202B",          // Blu notte medio-scuro per sfondo pagina
        cardBackground: "#192734",      // Blu scuro leggermente più chiaro per card o sezioni separate
        containerBg: "#0F1419",         // Colore container per raggruppare elementi
        highlight: "#21354A",           // Blu per evidenziare elementi o hover
        textPrimary: "#FFFFFF",         // Testo bianco principale
        textSecondary: "#AAB8C2",       // Testo secondario grigio chiaro
        accent: "#1A73E8",              // Blu per accenti principali
        shareButton: "#2D3748",         // Colore scuro per pulsante condividi

        // Mappatura per compatibilità con componenti esistenti
        bgMain: "#15202B",              // Background principale
        bgBox: "#192734",               // Background card
        bgHover: "#21354A",             // Hover
        textTitle: "#FFFFFF",           // Titoli
        textMuted: "#AAB8C2",           // Testo secondario

        // Bottoni
        btnPrimary: "#1A73E8",          // Bottone principale
        btnHover: "#1557B0",            // Hover bottone principale
        btnSecondary: "#21354A",        // Bottone secondario
        btnShare: "#2D3748",            // Bottone condividi
        btnShareHover: "#4A5568",       // Hover bottone condividi

        // Dropdown e form
        dropdownBg: "#192734",
        inputBg: "#21354A",
        inputBorder: "#AAB8C2",

        // Stati
        success: "#61DAFB",
        warning: "#F77F00",
        error: "#D62828",
        info: "#1A73E8",
      },
      fontFamily: {
        roboto: ["var(--font-roboto)"],
        inter: ["var(--font-inter)"], // Manteniamo per compatibilità
      },
      animation: {
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'slide-up': 'slideUp 0.3s ease-out',
        'bounce-subtle': 'bounceSubtle 2s infinite',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        bounceSubtle: {
          '0%, 100%': { transform: 'translateY(0)' },
          '50%': { transform: 'translateY(-5px)' },
        },
      },
      boxShadow: {
        'glow': '0 0 20px rgba(26, 115, 232, 0.3)',
        'glow-lg': '0 0 30px rgba(26, 115, 232, 0.4)',
        'glow-share': '0 0 20px rgba(45, 55, 72, 0.3)',
      },
    },
  },
  plugins: [],
};
