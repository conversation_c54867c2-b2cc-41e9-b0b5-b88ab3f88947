'use client';

import { createContext, useContext, useState, useEffect } from 'react';

// Definizione delle traduzioni
const translations = {
  it: {
    title: 'FM Challenger',
    subtitle: 'Genera sfide random per Football Manager',
    teamDifficulty: 'Difficoltà Campionato',
    challengeDifficulty: 'Difficoltà Sfida',
    select: 'Seleziona...',
    generateChallenge: 'Genera Sfida 🎲',
    regenerateChallenge: 'Rigenera Sfida 🔄',
    shareChallenge: 'Condividi Sfida 📷',
    loading: 'Generazione in corso... ⏳',
    yourChallenge: 'La Tua Sfida',
    team: '<PERSON>ra',
    challenges: 'Sfide',
    objective: 'Obiettivo 🏆',
    squad: 'Rosa 🏃',
    tactics: 'Tattica ⚽',
    footer: 'Creato con ❤️ per la community di Football Manager',
    errorTeam: 'Non è stato possibile trovare una squadra con la difficoltà selezionata.',
    errorChallenge: 'Non è stato possibile trovare sfide con la difficoltà selezionata.',
    errorGeneric: 'Si è verificato un errore durante la generazione della sfida.',
    filterByContinent: 'Filtra per continente:',
    activeFilter: 'Filtro attivo: verranno selezionate solo squadre dai continenti scelti',
    clickToLock: 'Clicca per bloccare',
    locked: 'Bloccato',
    aboutTitle: 'Cos\'è FM Challenger?',
    aboutDescription: 'FM Challenger è il generatore di sfide definitivo per Football Manager. Seleziona il livello di difficoltà per squadre e obiettivi, applica filtri per continente e genera sfide uniche per rendere le tue partite più emozionanti e imprevedibili.',
    aboutFeatures: 'Caratteristiche principali: difficoltà personalizzabili, filtri geografici, sistema di blocco per rigenerazioni selettive, salvataggio sfide e supporto multilingua.',
    feedbackTitle: 'Hai idee per nuove sfide?',
    feedbackSubtitle: 'Lascia un feedback',
    feedbackButton: 'Invia Feedback',
    veryEasy: 'Molto Facile',
    easy: 'Facile',
    medium: 'Media',
    hard: 'Difficile',
    veryHard: 'Molto Difficile',
    crazy: 'Matta',
    meme: 'Meme',
    profile: 'Profilo',
    savedChallenges: 'Sfide Salvate',
    activeChallenges: 'Sfide Attive',
    completedChallenges: 'Sfide Completate',
    archivedChallenges: 'Sfide Archiviate',
    challengeCompleted: 'Sfida Completata',
    archiveChallenge: 'Archivia',
    unarchiveChallenge: 'Ripristina',
    shareOnFacebook: 'Condividi su Facebook',
    language: 'Lingua',
  },
  en: {
    title: 'FM Challenger',
    subtitle: 'Generate random challenges for Football Manager',
    teamDifficulty: 'Team Difficulty',
    challengeDifficulty: 'Challenge Difficulty',
    select: 'Select...',
    generateChallenge: 'Generate Challenge 🎲',
    regenerateChallenge: 'Regenerate Challenge 🔄',
    shareChallenge: 'Share Challenge 📷',
    loading: 'Generating... ⏳',
    yourChallenge: 'Your Challenge',
    team: 'Team',
    challenges: 'Challenges',
    objective: 'Objective 🏆',
    squad: 'Squad 🏃',
    tactics: 'Tactics ⚽',
    footer: 'Created with ❤️ for the Football Manager community',
    errorTeam: 'Could not find a team with the selected difficulty.',
    errorChallenge: 'Could not find challenges with the selected difficulty.',
    errorGeneric: 'An error occurred while generating the challenge.',
    filterByContinent: 'Filter by continent:',
    activeFilter: 'Active filter: only teams from selected continents will be chosen',
    clickToLock: 'Click to lock',
    locked: 'Locked',
    aboutTitle: 'What is FM Challenger?',
    aboutDescription: 'FM Challenger is the ultimate challenge generator for Football Manager. Select difficulty levels for teams and objectives, apply continent filters, and generate unique challenges to make your games more exciting and unpredictable.',
    aboutFeatures: 'Key features: customizable difficulties, geographical filters, lock system for selective regeneration, challenge saving, and multilingual support.',
    feedbackTitle: 'Have ideas for new challenges?',
    feedbackSubtitle: 'Leave feedback',
    feedbackButton: 'Send Feedback',
    veryEasy: 'Very Easy',
    easy: 'Easy',
    medium: 'Medium',
    hard: 'Hard',
    veryHard: 'Very Hard',
    crazy: 'Crazy',
    meme: 'Meme',
    profile: 'Profile',
    savedChallenges: 'Saved Challenges',
    activeChallenges: 'Active Challenges',
    completedChallenges: 'Completed Challenges',
    archivedChallenges: 'Archived Challenges',
    challengeCompleted: 'Challenge Completed',
    archiveChallenge: 'Archive',
    unarchiveChallenge: 'Restore',
    shareOnFacebook: 'Share on Facebook',
    language: 'Language',
  },
};

// Creazione del context
const LanguageContext = createContext();

export function LanguageProvider({ children }) {
  const [language, setLanguage] = useState('it');
  const [t, setT] = useState(translations.it);

  // Rileva la lingua del browser all'avvio
  useEffect(() => {
    const detectBrowserLanguage = () => {
      if (typeof window !== 'undefined') {
        const browserLang = navigator.language.split('-')[0];
        return translations[browserLang] ? browserLang : 'en';
      }
      return 'en';
    };

    const detectedLang = detectBrowserLanguage();
    setLanguage(detectedLang);
    setT(translations[detectedLang]);
  }, []);

  // Aggiorna le traduzioni quando cambia la lingua
  useEffect(() => {
    setT(translations[language]);
  }, [language]);

  // Funzione per cambiare lingua
  const changeLanguage = (lang) => {
    if (translations[lang]) {
      setLanguage(lang);
      // Salva la preferenza dell'utente
      if (typeof window !== 'undefined') {
        localStorage.setItem('preferredLanguage', lang);
      }
    }
  };

  return (
    <LanguageContext.Provider value={{ language, t, changeLanguage }}>
      {children}
    </LanguageContext.Provider>
  );
}

// Hook personalizzato per utilizzare il context
export function useLanguage() {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
}

