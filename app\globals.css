@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #15202B;
  --foreground: #FFFFFF;
}

body {
  color: var(--foreground);
  background: var(--background);
  font-family: 'Roboto', sans-serif;
  scroll-behavior: smooth;
}

/* Scrollbar personalizzata */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #15202B;
}

::-webkit-scrollbar-thumb {
  background: #1A73E8;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #1557B0;
}

@layer components {
  .btn-primary {
    @apply bg-btnPrimary text-textPrimary py-3 px-6 rounded-lg hover:bg-btnHover transition-all duration-200 font-medium shadow-lg hover:shadow-glow transform hover:scale-105;
  }

  .btn-secondary {
    @apply bg-btnSecondary text-textPrimary py-3 px-6 rounded-lg hover:bg-highlight transition-all duration-200 font-medium shadow-lg hover:shadow-glow transform hover:scale-105;
  }

  .select-custom {
    @apply bg-inputBg text-textPrimary border border-inputBorder rounded-lg p-3 focus:outline-none focus:ring-2 focus:ring-accent focus:border-accent transition-all duration-200;
  }

  .card {
    @apply bg-bgBox p-6 rounded-xl border border-accent/20 shadow-lg hover:shadow-glow transition-all duration-300 backdrop-blur-sm;
  }

  .card-hover {
    @apply hover:bg-bgHover hover:border-accent/40 transform hover:scale-[1.02];
  }

  .input-field {
    @apply bg-inputBg text-textPrimary border border-inputBorder rounded-lg p-3 focus:outline-none focus:ring-2 focus:ring-accent focus:border-accent transition-all duration-200 placeholder-textMuted;
  }

  .gradient-text {
    @apply bg-gradient-to-r from-textTitle to-accent bg-clip-text text-transparent;
  }

  .glass-effect {
    @apply backdrop-blur-md bg-bgBox/80 border border-accent/20;
  }
}
