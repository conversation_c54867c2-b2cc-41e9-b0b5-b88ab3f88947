'use client';

import { useLanguage } from '../lib/LanguageContext';

export default function LockableItem({ title, content, isLocked, onToggleLock }) {
  const { t } = useLanguage();
  return (
    <div
      onClick={onToggleLock}
      className={`
        flex items-center justify-between p-3 rounded-md mb-2 cursor-pointer
        transition-all duration-200 ease-in-out
        ${isLocked
          ? 'border-2 border-accent bg-accent/10 shadow-md'
          : 'border border-textSecondary/20 bg-bgBox hover:border-accent/50 hover:bg-bgBox/80'}
      `}
      aria-label={isLocked ? `Sblocca questo elemento` : `Blocca questo elemento`}
    >
      <div className="flex-1">
        <h3 className="font-medium text-textTitle flex items-center">
          {title}
          <span className="ml-2 text-sm opacity-70">
            {isLocked ? `(${t.locked})` : `(${t.clickToLock})`}
          </span>
        </h3>
        <p className="text-textPrimary">{content}</p>
      </div>
      <div className="ml-4 text-xl">
        {isLocked ? '🔒' : '🔓'}
      </div>
    </div>
  );
}
