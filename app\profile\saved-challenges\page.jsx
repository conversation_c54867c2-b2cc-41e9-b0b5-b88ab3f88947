'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import { toast } from 'react-hot-toast';
import { useLanguage } from '../../../lib/LanguageContext';

export default function SavedChallengesPage() {
  const { data: session, status } = useSession();
  const { t, language } = useLanguage();
  const router = useRouter();
  const [challenges, setChallenges] = useState([]);
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);
  const [error, setError] = useState('');
  const [activeFilter, setActiveFilter] = useState('active'); // active, completed, archived

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin');
    }
  }, [status, router]);

  // Funzione per aggiornare lo stato di completamento di una sfida
  const toggleChallengeCompletion = async (challengeId, field, currentValue) => {
    if (updating) return;

    setUpdating(true);

    try {
      const response = await fetch('/api/challenges/update', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          challengeId,
          field,
          value: !currentValue,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Errore nell\'aggiornamento della sfida');
      }

      // Aggiorna lo stato locale
      setChallenges(prevChallenges =>
        prevChallenges.map(challenge =>
          challenge.id === challengeId
            ? { ...challenge, [field]: !currentValue }
            : challenge
        )
      );

      toast.success('Sfida aggiornata!');
    } catch (error) {
      console.error('Error updating challenge:', error);
      toast.error(error.message || 'Errore nell\'aggiornamento della sfida');
    } finally {
      setUpdating(false);
    }
  };

  // Funzione per archiviare/ripristinare una sfida
  const toggleArchiveChallenge = async (challengeId, currentArchived) => {
    if (updating) return;

    setUpdating(true);

    try {
      const response = await fetch('/api/challenges/update', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          challengeId,
          field: 'archived',
          value: !currentArchived,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Errore nell\'aggiornamento della sfida');
      }

      // Ricarica le sfide per il filtro corrente
      fetchChallenges(activeFilter);

      toast.success(currentArchived ? 'Sfida ripristinata!' : 'Sfida archiviata!');
    } catch (error) {
      console.error('Error archiving challenge:', error);
      toast.error(error.message || 'Errore nell\'aggiornamento della sfida');
    } finally {
      setUpdating(false);
    }
  };

  // Funzione per condividere su Facebook
  const shareOnFacebook = (challenge) => {
    const challengeText = `🏆 La mia sfida FM Challenger!\n\n` +
      `⚽ Squadra: ${challenge.team_name}\n` +
      `🎯 Obiettivo: ${challenge.objective_text}\n` +
      `👥 Rosa: ${challenge.squad_text}\n` +
      `⚡ Tattica: ${challenge.tactics_text}\n\n` +
      `Genera la tua sfida su FM Challenger!`;

    const url = encodeURIComponent(window.location.origin);
    const text = encodeURIComponent(challengeText);

    const facebookUrl = `https://www.facebook.com/sharer/sharer.php?u=${url}&quote=${text}`;

    window.open(facebookUrl, '_blank', 'width=600,height=400');
  };

  // Funzione per verificare se una sfida è completata
  const isChallengeCompleted = (challenge) => {
    return challenge.objective_completed && challenge.squad_completed && challenge.tactics_completed;
  };

  // Funzione per recuperare le sfide filtrate dall'API
  const fetchChallenges = async (filter = 'active') => {
    if (status !== 'authenticated') return;

    setLoading(true);
    setError('');

    try {
      const response = await fetch(`/api/challenges/filtered?filter=${filter}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Errore nel recupero delle sfide');
      }

      // Le sfide sono già filtrate dall'API
      setChallenges(data.challenges || []);
    } catch (error) {
      console.error('Error fetching challenges:', error);
      setError(error.message || 'Errore nel recupero delle sfide');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchChallenges(activeFilter);
  }, [status, activeFilter]);

  // Formatta la data in formato leggibile
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('it-IT', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen bg-bgMain flex items-center justify-center">
        <div className="text-center">
          <div className="h-12 w-12 border-4 border-accent border-t-transparent rounded-full animate-spin mx-auto"></div>
          <p className="mt-4 text-textPrimary">Caricamento...</p>
        </div>
      </div>
    );
  }

  if (!session) {
    return null;
  }

  return (
    <div className="min-h-screen bg-bgMain py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-6xl mx-auto">
        <div className="mb-8 flex items-center justify-between">
          <h1 className="text-2xl font-bold text-textTitle">{t.savedChallenges}</h1>
          <Link href="/profile" className="text-accent hover:underline flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
            </svg>
            {language === 'it' ? 'Torna al profilo' : 'Back to profile'}
          </Link>
        </div>

        {/* Filtri */}
        <div className="mb-8">
          <div className="flex flex-wrap gap-2">
            <button
              onClick={() => setActiveFilter('active')}
              className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
                activeFilter === 'active'
                  ? 'bg-accent text-white shadow-glow'
                  : 'bg-bgBox text-textSecondary hover:bg-bgHover hover:text-textPrimary'
              }`}
            >
              {t.activeChallenges}
            </button>
            <button
              onClick={() => setActiveFilter('completed')}
              className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
                activeFilter === 'completed'
                  ? 'bg-accent text-white shadow-glow'
                  : 'bg-bgBox text-textSecondary hover:bg-bgHover hover:text-textPrimary'
              }`}
            >
              {t.completedChallenges}
            </button>
            <button
              onClick={() => setActiveFilter('archived')}
              className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
                activeFilter === 'archived'
                  ? 'bg-accent text-white shadow-glow'
                  : 'bg-bgBox text-textSecondary hover:bg-bgHover hover:text-textPrimary'
              }`}
            >
              {t.archivedChallenges}
            </button>
          </div>
        </div>

        {error && (
          <div className="bg-red-900/20 border border-red-500/50 text-red-200 p-4 rounded-md mb-6">
            <p>{error}</p>
          </div>
        )}

        {challenges.length === 0 && !loading && !error ? (
          <div className="card p-8 text-center">
            <p className="text-textPrimary mb-4">
              {activeFilter === 'active' && (language === 'it' ? 'Non hai sfide attive.' : 'No active challenges.')}
              {activeFilter === 'completed' && (language === 'it' ? 'Non hai sfide completate.' : 'No completed challenges.')}
              {activeFilter === 'archived' && (language === 'it' ? 'Non hai sfide archiviate.' : 'No archived challenges.')}
            </p>
            <Link href="/" className="btn-primary inline-block">
              {language === 'it' ? 'Genera una nuova sfida' : 'Generate a new challenge'}
            </Link>
          </div>
        ) : (
          <div className="space-y-6">
            {challenges.map((challenge) => {
              const isCompleted = isChallengeCompleted(challenge);

              return (
                <div key={challenge.id} className={`card p-6 relative ${isCompleted ? 'ring-2 ring-green-500/50' : ''}`}>
                  {/* Badge Sfida Completata */}
                  {isCompleted && (
                    <div className="absolute top-4 right-4 bg-green-500 text-white px-3 py-1 rounded-full text-sm font-medium flex items-center">
                      <span className="mr-1">🏆</span>
                      {t.challengeCompleted}
                    </div>
                  )}

                  <div className="flex flex-col md:flex-row md:items-center mb-4">
                    <div className="flex items-center mb-4 md:mb-0 md:mr-6">
                      <div className="relative w-16 h-16 flex-shrink-0 mr-4">
                        {challenge.team_logo && (
                          <Image
                            src={challenge.team_logo}
                            alt={`Logo ${challenge.team_name}`}
                            fill
                            className="object-contain"
                          />
                        )}
                      </div>
                      <div>
                        <h2 className="text-xl font-semibold text-textTitle">{challenge.team_name}</h2>
                        <p className="text-textSecondary text-sm">
                          {challenge.team_country} - {challenge.team_league}
                        </p>
                      </div>
                    </div>
                    <div className="md:ml-auto text-textSecondary text-sm">
                      {language === 'it' ? 'Salvata il' : 'Saved on'} {formatDate(challenge.created_at)}
                    </div>
                  </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                  <div
                    className={`p-3 rounded-md cursor-pointer transition-all duration-200 ${
                      challenge.objective_completed
                        ? 'bg-green-900/20 border border-green-500/30'
                        : 'bg-bgBox/50 hover:bg-bgBox'
                    }`}
                    onClick={() => toggleChallengeCompletion(challenge.id, 'objective_completed', challenge.objective_completed)}
                  >
                    <h3 className="font-medium text-textTitle mb-2 flex items-center">
                      <span className="mr-2">
                        {challenge.objective_completed ? '✅' : '⬜'}
                      </span>
                      Obiettivo
                    </h3>
                    <p className={`text-textPrimary text-sm ${challenge.objective_completed ? 'line-through opacity-70' : ''}`}>
                      {challenge.objective_text}
                    </p>
                  </div>

                  <div
                    className={`p-3 rounded-md cursor-pointer transition-all duration-200 ${
                      challenge.squad_completed
                        ? 'bg-green-900/20 border border-green-500/30'
                        : 'bg-bgBox/50 hover:bg-bgBox'
                    }`}
                    onClick={() => toggleChallengeCompletion(challenge.id, 'squad_completed', challenge.squad_completed)}
                  >
                    <h3 className="font-medium text-textTitle mb-2 flex items-center">
                      <span className="mr-2">
                        {challenge.squad_completed ? '✅' : '⬜'}
                      </span>
                      Rosa
                    </h3>
                    <p className={`text-textPrimary text-sm ${challenge.squad_completed ? 'line-through opacity-70' : ''}`}>
                      {challenge.squad_text}
                    </p>
                  </div>

                  <div
                    className={`p-3 rounded-md cursor-pointer transition-all duration-200 ${
                      challenge.tactics_completed
                        ? 'bg-green-900/20 border border-green-500/30'
                        : 'bg-bgBox/50 hover:bg-bgBox'
                    }`}
                    onClick={() => toggleChallengeCompletion(challenge.id, 'tactics_completed', challenge.tactics_completed)}
                  >
                    <h3 className="font-medium text-textTitle mb-2 flex items-center">
                      <span className="mr-2">
                        {challenge.tactics_completed ? '✅' : '⬜'}
                      </span>
                      Tattica
                    </h3>
                    <p className={`text-textPrimary text-sm ${challenge.tactics_completed ? 'line-through opacity-70' : ''}`}>
                      {challenge.tactics_text}
                    </p>
                  </div>
                </div>

                {/* Pulsanti di azione */}
                <div className="flex flex-wrap gap-2 mt-6 pt-4 border-t border-textSecondary/10">
                  <button
                    onClick={() => shareOnFacebook(challenge)}
                    className="flex items-center px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-sm font-medium transition-colors"
                  >
                    <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                    </svg>
                    {t.shareOnFacebook}
                  </button>

                  <button
                    onClick={() => toggleArchiveChallenge(challenge.id, challenge.archived)}
                    disabled={updating}
                    className={`flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                      challenge.archived
                        ? 'bg-green-600 hover:bg-green-700 text-white'
                        : 'bg-orange-600 hover:bg-orange-700 text-white'
                    } ${updating ? 'opacity-50 cursor-not-allowed' : ''}`}
                  >
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      {challenge.archived ? (
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" />
                      ) : (
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 8l4 4 4-4" />
                      )}
                    </svg>
                    {challenge.archived ? t.unarchiveChallenge : t.archiveChallenge}
                  </button>
                </div>
              </div>
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
}
