import { NextResponse } from 'next/server';

export async function POST(request) {
  try {
    const { name, email, subject, message, challengeType } = await request.json();

    // Validazione base
    if (!name || !email || !subject || !message) {
      return NextResponse.json(
        { error: 'Tutti i campi sono obbligatori' },
        { status: 400 }
      );
    }

    // Qui potresti integrare con un servizio email come:
    // - Nodemailer
    // - SendGrid
    // - Resend
    // - O salvare nel database per revisione manuale

    // Per ora simuliamo l'invio dell'email
    const feedbackData = {
      name,
      email,
      subject,
      message,
      challengeType,
      timestamp: new Date().toISOString(),
      userAgent: request.headers.get('user-agent'),
      ip: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip')
    };

    // Log del feedback (in produzione potresti salvarlo in un database)
    console.log('Nuovo feedback ricevuto:', feedbackData);

    // Simula un piccolo delay per l'invio
    await new Promise(resolve => setTimeout(resolve, 1000));

    // In un'implementazione reale, qui invieresti l'email
    // Esempio con nodemailer:
    /*
    const transporter = nodemailer.createTransporter({
      // configurazione SMTP
    });

    await transporter.sendMail({
      from: process.env.SMTP_FROM,
      to: process.env.FEEDBACK_EMAIL,
      subject: `FM Challenger Feedback: ${subject}`,
      html: `
        <h2>Nuovo Feedback da FM Challenger</h2>
        <p><strong>Nome:</strong> ${name}</p>
        <p><strong>Email:</strong> ${email}</p>
        <p><strong>Tipo di sfida:</strong> ${challengeType}</p>
        <p><strong>Oggetto:</strong> ${subject}</p>
        <p><strong>Messaggio:</strong></p>
        <p>${message}</p>
        <hr>
        <p><small>Inviato il: ${new Date().toLocaleString()}</small></p>
      `
    });
    */

    return NextResponse.json(
      { message: 'Feedback inviato con successo' },
      { status: 200 }
    );

  } catch (error) {
    console.error('Errore nell\'invio del feedback:', error);
    return NextResponse.json(
      { error: 'Errore interno del server' },
      { status: 500 }
    );
  }
}
