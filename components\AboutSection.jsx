'use client';

import { useLanguage } from '../lib/LanguageContext';

export default function AboutSection() {
  const { t } = useLanguage();

  return (
    <div className="w-full">
      <div className="card">
        <div className="text-center mb-6">
          <h1 className="text-3xl md:text-4xl font-bold text-textTitle mb-4 gradient-text">
            {t.aboutTitle}
          </h1>
        </div>
        
        <div className="space-y-4 text-center">
          <p className="text-textPrimary text-lg leading-relaxed max-w-4xl mx-auto">
            {t.aboutDescription}
          </p>
          
          <p className="text-textSecondary leading-relaxed max-w-3xl mx-auto">
            {t.aboutFeatures}
          </p>
        </div>

        {/* Icone delle funzionalità */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-8">
          <div className="text-center p-4 bg-bgHover rounded-lg">
            <div className="text-2xl mb-2">🎯</div>
            <div className="text-sm text-textSecondary">Difficoltà Personalizzabili</div>
          </div>
          <div className="text-center p-4 bg-bgHover rounded-lg">
            <div className="text-2xl mb-2">🌍</div>
            <div className="text-sm text-textSecondary">Filtri Geografici</div>
          </div>
          <div className="text-center p-4 bg-bgHover rounded-lg">
            <div className="text-2xl mb-2">🔒</div>
            <div className="text-sm text-textSecondary">Sistema di Blocco</div>
          </div>
          <div className="text-center p-4 bg-bgHover rounded-lg">
            <div className="text-2xl mb-2">🌐</div>
            <div className="text-sm text-textSecondary">Multilingua</div>
          </div>
        </div>
      </div>
    </div>
  );
}
